@echo off
echo 🎯 RECOIL PATTERN DUMPER - BUILD SCRIPT
echo ======================================

REM Check if .NET SDK is available
dotnet --version >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ .NET SDK not found!
    echo.
    echo Please install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo ✅ .NET SDK found: 
dotnet --version
echo.

REM Navigate to project directory
cd "RecoilPatternDumper"

REM Clean previous builds
echo 🧹 Cleaning previous builds...
dotnet clean >nul 2>nul

REM Restore NuGet packages
echo 📦 Restoring NuGet packages...
dotnet restore

if %ERRORLEVEL% neq 0 (
    echo ❌ Package restore failed!
    pause
    exit /b 1
)

echo ✅ Packages restored successfully!
echo.

REM Build Debug configuration
echo 🔨 Building Debug configuration...
dotnet build --configuration Debug --no-restore

if %ERRORLEVEL% neq 0 (
    echo ❌ Debug build failed!
    pause
    exit /b 1
)

echo ✅ Debug build completed successfully!
echo.

REM Build Release configuration
echo 🔨 Building Release configuration...
dotnet build --configuration Release --no-restore

if %ERRORLEVEL% neq 0 (
    echo ❌ Release build failed!
    pause
    exit /b 1
)

echo ✅ Release build completed successfully!
echo.

REM Publish self-contained executable
echo 📦 Publishing self-contained executable...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "publish" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true

if %ERRORLEVEL% neq 0 (
    echo ❌ Publish failed!
    pause
    exit /b 1
)

echo ✅ Self-contained executable published successfully!
echo.

REM Show build results
echo 🎉 BUILD COMPLETED SUCCESSFULLY!
echo.
echo 📁 Output locations:
if exist "bin\Debug\net8.0-windows\RecoilPatternDumper.exe" (
    echo    Debug:   bin\Debug\net8.0-windows\RecoilPatternDumper.exe
)
if exist "bin\Release\net8.0-windows\RecoilPatternDumper.exe" (
    echo    Release: bin\Release\net8.0-windows\RecoilPatternDumper.exe
)
if exist "publish\RecoilPatternDumper.exe" (
    echo    Publish: publish\RecoilPatternDumper.exe (Self-contained)
)
echo.

REM Check for Python dependency
python --version >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Python not found in PATH!
    echo    The dumper requires Python for the dumping scripts.
    echo    Please install Python 3.8+ from: https://python.org
    echo.
) else (
    echo ✅ Python found: 
    python --version
    echo.
)

REM Ask if user wants to run the application
set /p choice="🚀 Run the Recoil Pattern Dumper now? (y/n): "
if /i "%choice%"=="y" (
    if exist "bin\Release\net8.0-windows\RecoilPatternDumper.exe" (
        echo Starting Recoil Pattern Dumper...
        start "" "bin\Release\net8.0-windows\RecoilPatternDumper.exe"
    ) else (
        echo ❌ Release executable not found!
    )
)

echo.
echo 💡 Usage Tips:
echo    1. Make sure Python is installed and in PATH
echo    2. Place the dumper scripts in the same directory
echo    3. Configure the Python path in the application
echo    4. Select weapons and run analysis
echo.
echo Press any key to exit...
pause >nul

cd ..
