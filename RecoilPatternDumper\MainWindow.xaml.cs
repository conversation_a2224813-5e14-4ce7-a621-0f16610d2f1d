// File: MainWindow.xaml.cs
// Made by Lag

using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Diagnostics;
using System.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RecoilPatternDumper.Models;
using RecoilPatternDumper.Services;
using MessageBox = System.Windows.MessageBox;

namespace RecoilPatternDumper
{
    public partial class MainWindow : Window
    {
        private DumperService? _dumperService;
        private readonly AnalysisService _analysisService;
        private readonly ExportService _exportService;
        private CancellationTokenSource? _cancellationTokenSource;
        private Dictionary<string, WeaponStatistics> _weaponStatistics = new();
        private List<OptimizedRecoilPattern> _optimizedPatterns = new();
        private List<DumpRunResult> _allResults = new();
        private readonly Stopwatch _analysisStopwatch = new();

        public MainWindow()
        {
            InitializeComponent();
            _analysisService = new AnalysisService();
            _exportService = new ExportService();

            // Set default game path
            GamePathTextBox.Text = @"E:\SteamLibrary\steamapps\common\Rust";
            ValidateGamePath();
        }

        #region Configuration Events

        private void BrowseGameButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "Select Rust Game Directory",
                ShowNewFolderButton = false
            };
            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                GamePathTextBox.Text = dialog.SelectedPath;
            }
        }

        private void GamePathTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ValidateGamePath();
        }

        private void RunCountSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (RunCountText != null)
                RunCountText.Text = $"{(int)e.NewValue} runs";
        }

        #endregion

        #region Analysis Events

        private async void StartAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateConfiguration())
            {
                MessageBox.Show("Please fix configuration issues before starting analysis.",
                    "Configuration Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                StartAnalysisButton.IsEnabled = false;
                StopAnalysisButton.IsEnabled = true;

                _cancellationTokenSource = new CancellationTokenSource();
                var runCount = (int)RunCountSlider.Value;
                _dumperService = new DumperService(GamePathTextBox.Text);
                _dumperService.ProgressUpdated += OnProgressUpdated;
                _dumperService.LogMessage += OnLogMessage;

                LogMessage("Starting recoil pattern analysis...");
                _analysisStopwatch.Restart();
                StatusText.Text = "Running dumps...";

                _allResults = await _dumperService.RunMultipleDumpsAsync(runCount, _cancellationTokenSource.Token);

                LogMessage("Analyzing results...");
                StatusText.Text = "Analyzing results...";

                _weaponStatistics = _analysisService.AnalyzeMultipleRuns(_allResults);
                _optimizedPatterns = _analysisService.GenerateOptimizedPatterns(_weaponStatistics);

                UpdateResultsTab();

                _analysisStopwatch.Stop();
                LogMessage($"Analysis completed in {_analysisStopwatch.Elapsed.TotalMinutes:F2} minutes");
                StatusText.Text = $"Analysis complete - {_weaponStatistics.Count} weapons analyzed";

                ExportButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                LogMessage($"Error during analysis: {ex.Message}");
                MessageBox.Show($"Analysis failed: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                StartAnalysisButton.IsEnabled = true;
                StopAnalysisButton.IsEnabled = false;
                AnalysisProgressBar.Value = 0;
                ProgressText.Text = "Ready";
            }
        }

        private void StopAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            _cancellationTokenSource?.Cancel();
            LogMessage("Analysis cancelled by user");
            StatusText.Text = "Analysis cancelled";
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.SaveFileDialog
            {
                Filter = "JSON files (*.json)|*.json",
                FileName = $"recoil_patterns_{DateTime.Now:yyyyMMdd_HHmmss}.json"
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                try
                {
                    await _exportService.ExportOptimizedPatternsAsync(_optimizedPatterns, dialog.FileName);
                    MessageBox.Show("Export completed successfully.", "Success",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Export failed: {ex.Message}", "Export Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void OnProgressUpdated(object? sender, DumpProgressEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                AnalysisProgressBar.Value = e.ProgressPercentage;
                ProgressText.Text = $"Run {e.CurrentRun}/{e.TotalRuns} ({e.SuccessfulRuns} success, {e.FailedRuns} failed)";
                if (_analysisStopwatch.IsRunning)
                    ElapsedTimeText.Text = $"Elapsed: {_analysisStopwatch.Elapsed:mm\\:ss}";
            });
        }

        private void OnLogMessage(object? sender, string message)
        {
            Dispatcher.Invoke(() => LogMessage(message));
        }

        private void UpdateResultsTab()
        {
            WeaponListBox.Items.Clear();

            foreach (var weaponName in _weaponStatistics
                     .OrderByDescending(w => w.Value.QualityScore)
                     .Select(w => w.Key))
            {
                WeaponListBox.Items.Add(weaponName);
            }

            if (WeaponListBox.Items.Count > 0)
                WeaponListBox.SelectedIndex = 0;
        }

        private void DisplayWeaponDetails(WeaponStatistics stats)
        {
            WeaponDetailsPanel.Children.Clear();

            var titleBlock = new TextBlock
            {
                Text = $"{stats.WeaponName} - Quality Score: {stats.QualityScore:F2}/100",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            WeaponDetailsPanel.Children.Add(titleBlock);

            // Additional stats can be added here.
        }

        private void WeaponListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (WeaponListBox.SelectedItem is string weaponName && _weaponStatistics.ContainsKey(weaponName))
            {
                DisplayWeaponDetails(_weaponStatistics[weaponName]);
            }
        }

        private bool ValidateConfiguration()
        {
            bool isValid = ValidateGamePath();
            if (StartAnalysisButton != null)
                StartAnalysisButton.IsEnabled = isValid;
            return isValid;
        }

        private bool ValidateGamePath()
        {
            string? path = GamePathTextBox?.Text;
            bool isValid = !string.IsNullOrWhiteSpace(path) && DumperService.ValidateRustGamePath(path);

            if (GameValidationText != null)
            {
                GameValidationText.Text = isValid
                    ? "✓ Rust game directory valid"
                    : "✗ Invalid game directory or missing content.bundle";
                GameValidationText.Foreground = isValid
                    ? System.Windows.Media.Brushes.LightGreen
                    : System.Windows.Media.Brushes.LightCoral;
            }

            return isValid;
        }

        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogTextArea.AppendText($"[{timestamp}] {message}\n");
            LogTextArea.ScrollToEnd();
        }

        #endregion
    }
}
