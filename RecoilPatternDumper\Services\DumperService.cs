using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using AssetsTools.NET;
using AssetsTools.NET.Extra;
using RecoilPatternDumper.Models;

namespace RecoilPatternDumper.Services
{
    public class DumpProgressEventArgs : EventArgs
    {
        public int CurrentRun { get; set; }
        public int TotalRuns { get; set; }
        public int SuccessfulRuns { get; set; }
        public int FailedRuns { get; set; }
        public double ProgressPercentage => TotalRuns == 0 ? 0 : (double)CurrentRun / TotalRuns * 100;
    }

    public class DumperService
    {
        private readonly string _gamePath;
        private readonly string _contentBundlePath;
        private readonly AssetsManager _assetsManager;

        public event EventHandler<DumpProgressEventArgs>? ProgressUpdated;
        public event EventHandler<string>? LogMessage;

        public DumperService(string gamePath)
        {
            _gamePath = gamePath;
            _contentBundlePath = Path.Combine(gamePath, "Bundles", "content.bundle");
            _assetsManager = new AssetsManager();
        }

        public static bool ValidateRustGamePath(string path) =>
            Directory.Exists(path) && File.Exists(Path.Combine(path, "Bundles", "content.bundle"));

        public async Task<List<DumpRunResult>> RunMultipleDumpsAsync(int runCount, CancellationToken cancellationToken)
        {
            var results = new List<DumpRunResult>();
            int successfulRuns = 0;
            int failedRuns = 0;

            LogMessage?.Invoke(this, "Starting dump runs...");

            for (int i = 1; i <= runCount; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    LogMessage?.Invoke(this, "Dump cancelled.");
                    break;
                }

                LogMessage?.Invoke(this, $"Run {i} of {runCount}: Loading bundle file...");

                try
                {
                    using var bundleInst = _assetsManager.LoadBundleFile(_contentBundlePath);
                    var patterns = await ExtractRecoilPatternsAsync(bundleInst, cancellationToken);

                    results.Add(new DumpRunResult
                    {
                        Timestamp = DateTime.Now,
                        WeaponPatterns = patterns
                    });

                    successfulRuns++;
                    LogMessage?.Invoke(this, $"Run {i}: Found {patterns.Count} weapon patterns.");
                }
                catch (Exception ex)
                {
                    failedRuns++;
                    LogMessage?.Invoke(this, $"Run {i} failed with error: {ex.Message}");
                }

                ProgressUpdated?.Invoke(this, new DumpProgressEventArgs
                {
                    CurrentRun = i,
                    TotalRuns = runCount,
                    SuccessfulRuns = successfulRuns,
                    FailedRuns = failedRuns
                });
            }

            LogMessage?.Invoke(this, $"Dump completed: {successfulRuns} successful, {failedRuns} failed runs.");
            return results;
        }

        private async Task<Dictionary<string, RecoilPattern>> ExtractRecoilPatternsAsync(BundleFileInstance bundle, CancellationToken cancellationToken)
        {
            var patterns = new Dictionary<string, RecoilPattern>();

            foreach (var assetsFile in bundle.file.GetAssetsFiles())
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var baseField = _assetsManager.GetBaseField(assetsFile);
                var weaponObjects = baseField.Get("m_Objects")?.Get("Array");

                if (weaponObjects == null)
                    continue;

                foreach (var obj in weaponObjects.GetChildrenList())
                {
                    var nameField = obj.Get("m_Name");
                    if (nameField != null && nameField.GetValue().AsString().Contains("weapon"))
                    {
                        var recoilData = ExtractRecoilData(obj);
                        if (recoilData != null)
                        {
                            patterns[recoilData.WeaponName] = recoilData;
                        }
                    }
                }
            }

            return await Task.FromResult(patterns);
        }

        private RecoilPattern? ExtractRecoilData(AssetTypeValueField field)
        {
            try
            {
                var recoilProps = field.Get("recoilProperties");
                if (recoilProps == null) return null;

                return new RecoilPattern
                {
                    WeaponName = field.Get("m_Name")?.GetValue().AsString() ?? string.Empty,
                    Points = ExtractRecoilPoints(recoilProps),
                    Accuracy = recoilProps.Get("accuracy")?.GetValue().AsFloat() ?? 0f,
                    Spread = recoilProps.Get("spread")?.GetValue().AsFloat() ?? 0f,
                    RecoilAmount = recoilProps.Get("recoilAmount")?.GetValue().AsFloat() ?? 0f
                };
            }
            catch
            {
                return null;
            }
        }

        private List<RecoilPoint> ExtractRecoilPoints(AssetTypeValueField recoilProps)
        {
            var points = new List<RecoilPoint>();
            var recoilPoints = recoilProps.Get("recoilPoints")?.Get("Array");

            if (recoilPoints != null)
            {
                foreach (var point in recoilPoints.GetChildrenList())
                {
                    points.Add(new RecoilPoint
                    {
                        X = point.Get("x")?.GetValue().AsFloat() ?? 0f,
                        Y = point.Get("y")?.GetValue().AsFloat() ?? 0f
                    });
                }
            }

            return points;
        }
    }
}
