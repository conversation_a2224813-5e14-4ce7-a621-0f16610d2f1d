using RecoilPatternDumper.Models;
using MathNet.Numerics.Statistics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RecoilPatternDumper.Services
{
    public class AnalysisService
    {
        public Dictionary<string, WeaponStatistics> AnalyzeMultipleRuns(List<DumpRunResult> results)
        {
            var weaponStats = new Dictionary<string, WeaponStatistics>();

            // Group successful runs by weapon name
            var successfulRuns = results?.Where(r => r.Success && r.Data != null).ToList() ?? new List<DumpRunResult>();

            if (!successfulRuns.Any())
                return weaponStats;

            // Get all unique weapon names
            var allWeaponNames = successfulRuns
                .SelectMany(r => r.Data!.Weapons.Select(w => w.Name))
                .Distinct()
                .ToList();

            foreach (var weaponName in allWeaponNames)
            {
                var weaponDataAcrossRuns = successfulRuns
                    .Select(r => r.Data!.Weapons.FirstOrDefault(w => w.Name == weaponName))
                    .Where(w => w != null)
                    .ToList();

                if (weaponDataAcrossRuns.Any())
                {
                    weaponStats[weaponName] = StatisticalAnalyzer.AnalyzeWeapon(weaponName, weaponDataAcrossRuns!);
                }
            }

            return weaponStats;
        }

        public List<OptimizedRecoilPattern> GenerateOptimizedPatterns(Dictionary<string, WeaponStatistics> weaponStats)
        {
            var optimizedPatterns = new List<OptimizedRecoilPattern>();

            if (weaponStats == null || !weaponStats.Any())
                return optimizedPatterns;

            foreach (var kvp in weaponStats)
            {
                var weaponName = kvp.Key;
                var stats = kvp.Value;

                var pattern = new OptimizedRecoilPattern
                {
                    WeaponName = weaponName,
                    OptimalPitchPattern = new List<double>(stats.OptimalPitchCurve ?? new List<double>()),
                    OptimalYawPattern = new List<double>(stats.OptimalYawCurve ?? new List<double>()),
                    ConfidenceScore = stats.QualityScore,
                    BasedOnRuns = stats.TotalRuns,
                    GeneratedAt = DateTime.Now,
                    OptimalProperties = new RecoilProperties
                    {
                        YawMax = stats.RecoilStats?.YawMaxMean ?? 0,
                        YawMin = stats.RecoilStats?.YawMinMean ?? 0,
                        PitchMax = stats.RecoilStats?.PitchMaxMean ?? 0,
                        PitchMin = stats.RecoilStats?.PitchMinMean ?? 0,
                        TimeMin = stats.RecoilStats?.TimeMinMean ?? 0,
                        TimeMax = stats.RecoilStats?.TimeMaxMean ?? 0,
                        AdsScale = stats.RecoilStats?.AdsScaleMean ?? 0,
                        MaxRadius = stats.RecoilStats?.MaxRadiusMean ?? 0
                    }
                };

                optimizedPatterns.Add(pattern);
            }

            return optimizedPatterns.OrderByDescending(p => p.ConfidenceScore).ToList();
        }

        public string GenerateDetailedReport(Dictionary<string, WeaponStatistics> weaponStats, List<DumpRunResult> allResults)
        {
            var report = new StringBuilder();

            allResults ??= new List<DumpRunResult>();
            weaponStats ??= new Dictionary<string, WeaponStatistics>();

            report.AppendLine("=== RECOIL PATTERN ANALYSIS REPORT ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"Total Runs: {allResults.Count}");
            report.AppendLine($"Successful Runs: {allResults.Count(r => r.Success)}");
            report.AppendLine($"Failed Runs: {allResults.Count(r => !r.Success)}");
            report.AppendLine();

            // Execution time statistics
            var successfulRuns = allResults.Where(r => r.Success).ToList();
            if (successfulRuns.Any())
            {
                var executionTimes = successfulRuns.Select(r => r.ExecutionTime.TotalSeconds).ToArray();
                if (executionTimes.Length > 0)
                {
                    report.AppendLine("=== EXECUTION TIME STATISTICS ===");
                    report.AppendLine($"Average Execution Time: {executionTimes.Mean():F2} seconds");
                    report.AppendLine($"Min Execution Time: {executionTimes.Min():F2} seconds");
                    report.AppendLine($"Max Execution Time: {executionTimes.Max():F2} seconds");
                    report.AppendLine($"Standard Deviation: {executionTimes.StandardDeviation():F2} seconds");
                    report.AppendLine();
                }
            }

            // Weapon statistics
            report.AppendLine("=== WEAPON ANALYSIS ===");
            if (weaponStats.Any())
            {
                foreach (var kvp in weaponStats.OrderByDescending(w => w.Value.QualityScore))
                {
                    var weaponName = kvp.Key;
                    var stats = kvp.Value;

                    report.AppendLine($"\n--- {weaponName} ---");
                    report.AppendLine($"Quality Score: {stats.QualityScore:F2}/100");
                    report.AppendLine($"Based on {stats.TotalRuns} successful runs");

                    report.AppendLine("\nRecoil Properties:");
                    report.AppendLine($"  Yaw Max: {stats.RecoilStats?.YawMaxMean:F4} ± {stats.RecoilStats?.YawMaxStdDev:F4}");
                    report.AppendLine($"  Yaw Min: {stats.RecoilStats?.YawMinMean:F4} ± {stats.RecoilStats?.YawMinStdDev:F4}");
                    report.AppendLine($"  Pitch Max: {stats.RecoilStats?.PitchMaxMean:F4} ± {stats.RecoilStats?.PitchMaxStdDev:F4}");
                    report.AppendLine($"  Pitch Min: {stats.RecoilStats?.PitchMinMean:F4} ± {stats.RecoilStats?.PitchMinStdDev:F4}");
                    report.AppendLine($"  Time Min: {stats.RecoilStats?.TimeMinMean:F2} ± {stats.RecoilStats?.TimeMinStdDev:F2} ms");
                    report.AppendLine($"  Time Max: {stats.RecoilStats?.TimeMaxMean:F2} ± {stats.RecoilStats?.TimeMaxStdDev:F2} ms");
                    report.AppendLine($"  ADS Scale: {stats.RecoilStats?.AdsScaleMean:F4} ± {stats.RecoilStats?.AdsScaleStdDev:F4}");
                    report.AppendLine($"  Max Radius: {stats.RecoilStats?.MaxRadiusMean:F4} ± {stats.RecoilStats?.MaxRadiusStdDev:F4}");

                    report.AppendLine("\nOther Properties:");
                    report.AppendLine($"  Repeat Delay: {stats.RepeatDelayMean:F2} ± {stats.RepeatDelayStdDev:F2} ms");
                    report.AppendLine($"  Move Penalty: {stats.MovePenaltyMean:F4} ± {stats.MovePenaltyStdDev:F4}");

                    // Pattern consistency analysis
                    if (stats.OptimalPitchCurve?.Any() == true &&
                        stats.PitchCurveStdDev?.Any() == true &&
                        stats.YawCurveStdDev?.Any() == true)
                    {
                        var avgPitchStdDev = stats.PitchCurveStdDev.Average();
                        var avgYawStdDev = stats.YawCurveStdDev.Average();

                        report.AppendLine("\nPattern Consistency:");
                        report.AppendLine($"  Average Pitch Variation: {avgPitchStdDev:F4}");
                        report.AppendLine($"  Average Yaw Variation: {avgYawStdDev:F4}");
                        report.AppendLine($"  Pattern Length: {stats.OptimalPitchCurve.Count} shots");
                    }
                }
            }
            else
            {
                report.AppendLine("No weapon statistics available.");
            }

            // Failed runs analysis
            var failedRuns = allResults.Where(r => !r.Success).ToList();
            if (failedRuns.Any())
            {
                report.AppendLine("\n=== FAILED RUNS ANALYSIS ===");
                var errorGroups = failedRuns.GroupBy(r => string.IsNullOrEmpty(r.ErrorMessage) ? "Unknown Error" : r.ErrorMessage).ToList();

                foreach (var errorGroup in errorGroups)
                {
                    report.AppendLine($"Error: {errorGroup.Key}");
                    report.AppendLine($"Occurrences: {errorGroup.Count()}");
                    report.AppendLine($"Run Numbers: {string.Join(", ", errorGroup.Select(r => r.RunNumber))}");
                    report.AppendLine();
                }
            }

            return report.ToString();
        }

        public Dictionary<string, object> GenerateComparisonMetrics(Dictionary<string, WeaponStatistics> weaponStats)
        {
            var metrics = new Dictionary<string, object>();

            if (weaponStats == null || !weaponStats.Any())
                return metrics;

            // Overall quality metrics
            var qualityScores = weaponStats.Values.Select(w => w.QualityScore).ToArray();
            if (qualityScores.Length > 0)
            {
                metrics["OverallQuality"] = new
                {
                    Average = qualityScores.Mean(),
                    Best = qualityScores.Max(),
                    Worst = qualityScores.Min(),
                    StandardDeviation = qualityScores.StandardDeviation()
                };
            }

            // Most consistent weapons
            var topWeapons = weaponStats
                .OrderByDescending(w => w.Value.QualityScore)
                .Take(5)
                .Select(w => new { Name = w.Key, Score = w.Value.QualityScore })
                .ToList();
            metrics["TopWeapons"] = topWeapons;

            // Recoil intensity comparison
            var recoilIntensities = weaponStats.ToDictionary(
                w => w.Key,
                w => Math.Sqrt(
                    Math.Pow(w.Value.RecoilStats?.PitchMaxMean ?? 0, 2) +
                    Math.Pow(w.Value.RecoilStats?.YawMaxMean ?? 0, 2))
            );
            metrics["RecoilIntensities"] = recoilIntensities.OrderByDescending(r => r.Value).ToList();

            return metrics;
        }
    }
}
