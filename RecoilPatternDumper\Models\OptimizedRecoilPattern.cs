using System.Collections.Generic;

namespace RecoilPatternDumper.Models
{
    public class OptimizedRecoilPattern
    {
        public string WeaponName { get; set; } = string.Empty;
        public List<RecoilPoint> Points { get; set; } = new();
        public double Accuracy { get; set; }
        public double Spread { get; set; }
        public double QualityScore { get; set; }
        public double Confidence { get; set; }
        public List<double> PitchCurve { get; set; } = new();
        public List<double> YawCurve { get; set; } = new();
    }
}
