// File: WeaponStatistics.cs
// Made by Lag

using System;
using System.Collections.Generic;

namespace RecoilPatternDumper.Models
{
    public class WeaponStatistics
    {
        public string WeaponName { get; set; } = string.Empty;

        // Total successful runs analyzed for this weapon
        public int TotalRuns { get; set; }

        // Quality score of the analysis (0-100 scale)
        public double QualityScore { get; set; }

        // Recoil statistics group (means, stddev, etc)
        public RecoilStats? RecoilStats { get; set; }

        // Average delay between repeated shots
        public double RepeatDelayMean { get; set; }
        public double RepeatDelayStdDev { get; set; }

        // Movement penalty stats
        public double MovePenaltyMean { get; set; }
        public double MovePenaltyStdDev { get; set; }

        // Curves representing optimal pitch and yaw recoil over time
        public List<double>? OptimalPitchCurve { get; set; } = new List<double>();
        public List<double>? OptimalYawCurve { get; set; } = new List<double>();

        // Standard deviations of curves (variation over runs)
        public List<double>? PitchCurveStdDev { get; set; } = new List<double>();
        public List<double>? YawCurveStdDev { get; set; } = new List<double>();

        // Additional stats for confidence or other metrics
        public double ConfidenceScore { get; set; }

        // Static factory method for analysis (stub - implement your logic)
        public static WeaponStatistics AnalyzeWeapon(string weaponName, List<Weapon> weaponData)
        {
            // TODO: Implement actual analysis logic here
            var stats = new WeaponStatistics
            {
                WeaponName = weaponName,
                TotalRuns = weaponData.Count,
                // Fill other fields by processing weaponData...
            };

            // Example: Calculate quality score placeholder
            stats.QualityScore = CalculateQualityScore(stats);

            // Calculate optimal curves and other stats
            CalculateOptimalCurves(weaponData, stats);

            return stats;
        }

        private static void CalculateOptimalCurves(List<Weapon> weaponData, WeaponStatistics stats)
        {
            // TODO: Implement curve calculations
        }

        private static double CalculateQualityScore(WeaponStatistics stats)
        {
            // TODO: Implement your quality score logic
            return 100.0;
        }
    }

    public class RecoilStats
    {
        public double YawMaxMean { get; set; }
        public double YawMaxStdDev { get; set; }
        public double YawMinMean { get; set; }
        public double YawMinStdDev { get; set; }
        public double PitchMaxMean { get; set; }
        public double PitchMaxStdDev { get; set; }
        public double PitchMinMean { get; set; }
        public double PitchMinStdDev { get; set; }
        public double TimeMinMean { get; set; }
        public double TimeMinStdDev { get; set; }
        public double TimeMaxMean { get; set; }
        public double TimeMaxStdDev { get; set; }
        public double AdsScaleMean { get; set; }
        public double AdsScaleStdDev { get; set; }
        public double MaxRadiusMean { get; set; }
        public double MaxRadiusStdDev { get; set; }
    }
}
