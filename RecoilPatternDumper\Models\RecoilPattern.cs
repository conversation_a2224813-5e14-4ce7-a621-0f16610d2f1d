using System.Collections.Generic;

namespace RecoilPatternDumper.Models
{
    public class RecoilPattern
    {
        public string WeaponName { get; set; } = string.Empty;
        public List<RecoilPoint> Points { get; set; } = new();
        public float Accuracy { get; set; }
        public float Spread { get; set; }
        public float RecoilAmount { get; set; }
    }

    public class RecoilPoint
    {
        public float X { get; set; }
        public float Y { get; set; }

        public RecoilPoint() { }

        public RecoilPoint(float x, float y)
        {
            X = x;
            Y = y;
        }
    }
}
