#include "views/MainWindowScripts.h"

namespace octane {
namespace views {

std::string MainWindowScripts::getJavaScriptContent()
{
    return R"(
<script>
    // Global state
    let currentTab = 'main';
    let recoilEnabled = false;
    let esp32Connected = false;

    // Safe message posting function
    function postMessageToNative(message) {
        try {
            if (window.chrome && window.chrome.webview && window.chrome.webview.postMessage) {
                window.chrome.webview.postMessage(message);
            } else {
                console.log('WebView2 not available, message:', message);
            }
        } catch (error) {
            console.error('Error posting message to native:', error);
        }
    }

    // Tab switching functionality
    function showTab(tabName) {
        try {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.classList.remove('active');
        });

        // Show selected tab content
        const selectedTab = document.getElementById(tabName + '-tab');
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // Add active class to selected tab button
        const selectedButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
        }

            currentTab = tabName;

            // Notify C++ about tab change
            postMessageToNative({
                type: 'tabChanged',
                tab: tabName
            });
        } catch (error) {
            console.error('Error in showTab:', error);
        }
    }

    // Weapon selection change
    function onWeaponChange() {
        try {
            const weapon = document.getElementById('primary-weapon').value;
            console.log('Weapon changed to:', weapon);

            postMessageToNative({
                type: 'weaponChanged',
                weapon: weapon
            });
        } catch (error) {
            console.error('Error in onWeaponChange:', error);
        }
    }

    // Sight selection change
    function onSightChange() {
        const sight = document.getElementById('sight').value;
        console.log('Sight changed to:', sight);
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'sightChanged',
                sight: sight
            });
        }
    }

    // Muzzle selection change
    function onMuzzleChange() {
        const muzzle = document.getElementById('muzzle').value;
        console.log('Muzzle changed to:', muzzle);
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'muzzleChanged',
                muzzle: muzzle
            });
        }
    }

    // Recoil compensation slider
    function onRecoilCompensationChange() {
        const value = document.getElementById('recoil-compensation').value;
        document.getElementById('recoil-compensation-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'recoilCompensationChanged',
                value: parseInt(value)
            });
        }
    }

    // Humanization slider
    function onHumanizationChange() {
        const value = document.getElementById('humanization').value;
        document.getElementById('humanization-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'humanizationChanged',
                value: parseInt(value)
            });
        }
    }

    // Smoothing slider
    function onSmoothingChange() {
        const value = document.getElementById('smoothing').value;
        document.getElementById('smoothing-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'smoothingChanged',
                value: parseInt(value)
            });
        }
    }

    // Hip fire checkbox
    function onHipFireChange() {
        const checked = document.getElementById('hip-fire').checked;
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'hipFireChanged',
                enabled: checked
            });
        }
    }

    // Toggle recoil control
    function toggleRecoil() {
        recoilEnabled = !recoilEnabled;
        const button = document.getElementById('recoil-toggle');
        
        if (recoilEnabled) {
            button.textContent = 'ENABLED';
            button.style.background = 'linear-gradient(135deg, #00ff88 0%, #00d4ff 100%)';
        } else {
            button.textContent = 'DISABLED';
            button.style.background = 'linear-gradient(135deg, #00d4ff 0%, #00ff88 100%)';
        }
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'recoilToggled',
                enabled: recoilEnabled
            });
        }
    }

    // Sensitivity slider
    function onSensitivityChange() {
        const value = document.getElementById('sensitivity').value;
        document.getElementById('sensitivity-value').textContent = value;
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'sensitivityChanged',
                value: parseFloat(value)
            });
        }
    }

    // FOV slider
    function onFovChange() {
        const value = document.getElementById('fov').value;
        document.getElementById('fov-value').textContent = value;
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'fovChanged',
                value: parseInt(value)
            });
        }
    }

    // Horizontal multiplier slider
    function onHorizontalMultiplierChange() {
        const value = document.getElementById('horizontal-multiplier').value;
        document.getElementById('horizontal-multiplier-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'horizontalMultiplierChanged',
                value: parseInt(value)
            });
        }
    }

    // Vertical multiplier slider
    function onVerticalMultiplierChange() {
        const value = document.getElementById('vertical-multiplier').value;
        document.getElementById('vertical-multiplier-value').textContent = value + '%';
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'verticalMultiplierChanged',
                value: parseInt(value)
            });
        }
    }

    // Refresh ports
    function refreshPorts() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'refreshPorts'
            });
        }
    }

    // Toggle ESP32 connection
    function toggleConnection() {
        const port = document.getElementById('port-select').value;
        if (!port && !esp32Connected) {
            alert('Please select a port first');
            return;
        }
        
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'toggleConnection',
                port: port,
                connected: esp32Connected
            });
        }
    }

    // Update connection status (called from C++)
    function updateConnectionStatus(connected, port) {
        esp32Connected = connected;
        const statusIndicator = document.getElementById('connection-status');
        const statusText = document.getElementById('connection-text');
        const connectBtn = document.getElementById('connect-btn');
        
        if (connected) {
            statusIndicator.className = 'status-indicator connected';
            statusText.textContent = `Connected to ${port}`;
            connectBtn.textContent = 'Disconnect';
        } else {
            statusIndicator.className = 'status-indicator disconnected';
            statusText.textContent = 'Disconnected';
            connectBtn.textContent = 'Connect';
        }
    }

    // Update available ports (called from C++)
    function updatePorts(ports) {
        const portSelect = document.getElementById('port-select');
        portSelect.innerHTML = '<option value="">Select Port...</option>';
        
        ports.forEach(port => {
            const option = document.createElement('option');
            option.value = port;
            option.textContent = port;
            portSelect.appendChild(option);
        });
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Octane Recoil Controller initialized');
        
        // Request initial data from C++
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.postMessage({
                type: 'initialized'
            });
        }
    });
</script>
)";
}

} // namespace views
} // namespace octane
