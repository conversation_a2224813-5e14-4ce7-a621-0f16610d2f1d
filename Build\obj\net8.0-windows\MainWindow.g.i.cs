﻿#pragma checksum "..\..\..\RecoilPatternDumper\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FF30A33FD1AD893EC706F21F16EC1168E83727DB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilPatternDumper {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 17 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseGameButton;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GamePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GameValidationText;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RunCountText;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider RunCountSlider;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WeaponListBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WeaponDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextArea;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartAnalysisButton;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopAnalysisButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ElapsedTimeText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar AnalysisProgressBar;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Dumper;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BrowseGameButton = ((System.Windows.Controls.Button)(target));
            
            #line 18 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.BrowseGameButton.Click += new System.Windows.RoutedEventHandler(this.BrowseGameButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.GamePathTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 19 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.GamePathTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.GamePathTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.GameValidationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.RunCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.RunCountSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 28 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.RunCountSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.RunCountSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.WeaponListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 41 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.WeaponListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WeaponListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.WeaponDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.LogTextArea = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.StartAnalysisButton = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.StartAnalysisButton.Click += new System.Windows.RoutedEventHandler(this.StartAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.StopAnalysisButton = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.StopAnalysisButton.Click += new System.Windows.RoutedEventHandler(this.StopAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\RecoilPatternDumper\MainWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ElapsedTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.AnalysisProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 15:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

