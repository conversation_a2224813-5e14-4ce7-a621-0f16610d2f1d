<Window x:Class="RecoilPatternDumper.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Recoil Pattern Dumper" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Configuration Panel -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <DockPanel Margin="0,5">
                <TextBlock Text="Game Path:" VerticalAlignment="Center" Width="100"/>
                <Button x:Name="BrowseGameButton" Content="Browse" Width="80" 
                        Margin="5,0" DockPanel.Dock="Right" Click="BrowseGameButton_Click"/>
                <TextBox x:Name="GamePathTextBox" TextChanged="GamePathTextBox_TextChanged"/>
            </DockPanel>
            <TextBlock x:Name="GameValidationText" Margin="100,0,0,10"/>

            <DockPanel Margin="0,5">
                <TextBlock Text="Run Count:" VerticalAlignment="Center" Width="100"/>
                <TextBlock x:Name="RunCountText" Text="10 runs" Width="60" 
                          VerticalAlignment="Center" DockPanel.Dock="Right"/>
                <Slider x:Name="RunCountSlider" Minimum="1" Maximum="100" Value="10" 
                        ValueChanged="RunCountSlider_ValueChanged"/>
            </DockPanel>
        </StackPanel>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Results List -->
            <DockPanel Grid.Column="0" Margin="0,0,10,0">
                <ListBox x:Name="WeaponListBox" SelectionChanged="WeaponListBox_SelectionChanged"/>
            </DockPanel>

            <!-- Details and Log -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="150"/>
                </Grid.RowDefinitions>

                <ScrollViewer Grid.Row="0">
                    <StackPanel x:Name="WeaponDetailsPanel"/>
                </ScrollViewer>

                <TextBox x:Name="LogTextArea" Grid.Row="1" IsReadOnly="True" 
                         TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                         Margin="0,5,0,0"/>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <StackPanel Grid.Row="2" Margin="0,10,0,0">
            <DockPanel>
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <Button x:Name="StartAnalysisButton" Content="Start Analysis" 
                            Width="100" Click="StartAnalysisButton_Click"/>
                    <Button x:Name="StopAnalysisButton" Content="Stop" Width="80" 
                            Margin="5,0" IsEnabled="False" Click="StopAnalysisButton_Click"/>
                    <Button x:Name="ExportButton" Content="Export" Width="80" 
                            Margin="5,0" IsEnabled="False" Click="ExportButton_Click"/>
                </StackPanel>
                <StackPanel>
                    <TextBlock x:Name="StatusText" Text="Ready"/>
                    <TextBlock x:Name="ElapsedTimeText"/>
                    <ProgressBar x:Name="AnalysisProgressBar" Height="20" Margin="0,5"/>
                    <TextBlock x:Name="ProgressText"/>
                </StackPanel>
            </DockPanel>
        </StackPanel>
    </Grid>
</Window>
