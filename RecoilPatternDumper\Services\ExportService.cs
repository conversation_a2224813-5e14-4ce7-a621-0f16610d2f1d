using System.IO;
using System.Text;
using Newtonsoft.Json;
using RecoilPatternDumper.Models;

namespace RecoilPatternDumper.Services
{
    public class ExportService
    {
        public async Task ExportOptimizedPatternsAsync(List<OptimizedRecoilPattern> patterns, string filePath)
        {
            var json = JsonConvert.SerializeObject(patterns, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json);
        }

        public async Task ExportDetailedReportAsync(string report, string filePath)
        {
            await File.WriteAllTextAsync(filePath, report);
        }

        public async Task ExportCsvDataAsync(Dictionary<string, WeaponStatistics> weaponStats, string filePath)
        {
            var csv = new StringBuilder();
            
            // Header
            csv.AppendLine("WeaponName,QualityScore,TotalRuns,YawMaxMean,YawMaxStdDev,PitchMaxMean,PitchMaxStdDev," +
                          "TimeMinMean,TimeMaxMean,RepeatDelayMean,MovePenaltyMean,AdsScaleMean,MaxRadiusMean");

            // Data rows
            foreach (var kvp in weaponStats)
            {
                var weapon = kvp.Key;
                var stats = kvp.Value;
                
                csv.AppendLine($"{weapon},{stats.QualityScore:F2},{stats.TotalRuns}," +
                              $"{stats.RecoilStats.YawMaxMean:F4},{stats.RecoilStats.YawMaxStdDev:F4}," +
                              $"{stats.RecoilStats.PitchMaxMean:F4},{stats.RecoilStats.PitchMaxStdDev:F4}," +
                              $"{stats.RecoilStats.TimeMinMean:F2},{stats.RecoilStats.TimeMaxMean:F2}," +
                              $"{stats.RepeatDelayMean:F2},{stats.MovePenaltyMean:F4}," +
                              $"{stats.RecoilStats.AdsScaleMean:F4},{stats.RecoilStats.MaxRadiusMean:F4}");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString());
        }

        public async Task ExportRecoilPatternsForESP32Async(List<OptimizedRecoilPattern> patterns, string filePath)
        {
            var cppCode = new StringBuilder();
            
            cppCode.AppendLine("// Auto-generated optimized recoil patterns");
            cppCode.AppendLine($"// Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            cppCode.AppendLine("// Based on statistical analysis of multiple dump runs");
            cppCode.AppendLine();
            cppCode.AppendLine("#ifndef RECOIL_PATTERNS_H");
            cppCode.AppendLine("#define RECOIL_PATTERNS_H");
            cppCode.AppendLine();
            cppCode.AppendLine("#include <Arduino.h>");
            cppCode.AppendLine();

            foreach (var pattern in patterns.Take(10)) // Limit to top 10 weapons
            {
                var weaponName = SanitizeVariableName(pattern.WeaponName);
                
                cppCode.AppendLine($"// {pattern.WeaponName} - Quality Score: {pattern.ConfidenceScore:F2}/100");
                cppCode.AppendLine($"// Based on {pattern.BasedOnRuns} dump runs");
                
                // Pitch pattern
                cppCode.AppendLine($"const float {weaponName}_pitch_pattern[] = {{");
                for (int i = 0; i < pattern.OptimalPitchPattern.Count; i++)
                {
                    var value = pattern.OptimalPitchPattern[i];
                    var comma = i < pattern.OptimalPitchPattern.Count - 1 ? "," : "";
                    cppCode.AppendLine($"    {value:F6}f{comma}");
                }
                cppCode.AppendLine("};");
                cppCode.AppendLine();

                // Yaw pattern
                cppCode.AppendLine($"const float {weaponName}_yaw_pattern[] = {{");
                for (int i = 0; i < pattern.OptimalYawPattern.Count; i++)
                {
                    var value = pattern.OptimalYawPattern[i];
                    var comma = i < pattern.OptimalYawPattern.Count - 1 ? "," : "";
                    cppCode.AppendLine($"    {value:F6}f{comma}");
                }
                cppCode.AppendLine("};");
                cppCode.AppendLine();

                // Pattern info struct
                cppCode.AppendLine($"const struct {{");
                cppCode.AppendLine($"    const char* name;");
                cppCode.AppendLine($"    const float* pitch_pattern;");
                cppCode.AppendLine($"    const float* yaw_pattern;");
                cppCode.AppendLine($"    int pattern_length;");
                cppCode.AppendLine($"    float confidence_score;");
                cppCode.AppendLine($"    float time_min;");
                cppCode.AppendLine($"    float time_max;");
                cppCode.AppendLine($"    float ads_scale;");
                cppCode.AppendLine($"}} {weaponName}_info = {{");
                cppCode.AppendLine($"    \"{pattern.WeaponName}\",");
                cppCode.AppendLine($"    {weaponName}_pitch_pattern,");
                cppCode.AppendLine($"    {weaponName}_yaw_pattern,");
                cppCode.AppendLine($"    {pattern.OptimalPitchPattern.Count},");
                cppCode.AppendLine($"    {pattern.ConfidenceScore:F2}f,");
                cppCode.AppendLine($"    {pattern.OptimalProperties.TimeMin:F2}f,");
                cppCode.AppendLine($"    {pattern.OptimalProperties.TimeMax:F2}f,");
                cppCode.AppendLine($"    {pattern.OptimalProperties.AdsScale:F4}f");
                cppCode.AppendLine("};");
                cppCode.AppendLine();
            }

            // Create weapon lookup array
            cppCode.AppendLine("// Weapon lookup array");
            cppCode.AppendLine("const void* weapon_patterns[] = {");
            foreach (var pattern in patterns.Take(10))
            {
                var weaponName = SanitizeVariableName(pattern.WeaponName);
                cppCode.AppendLine($"    &{weaponName}_info,");
            }
            cppCode.AppendLine("};");
            cppCode.AppendLine();
            cppCode.AppendLine($"const int WEAPON_COUNT = {Math.Min(patterns.Count, 10)};");
            cppCode.AppendLine();
            cppCode.AppendLine("#endif // RECOIL_PATTERNS_H");

            await File.WriteAllTextAsync(filePath, cppCode.ToString());
        }

        public async Task ExportComparisonChartDataAsync(Dictionary<string, WeaponStatistics> weaponStats, string filePath)
        {
            var chartData = new
            {
                labels = weaponStats.Keys.ToArray(),
                datasets = new[]
                {
                    new
                    {
                        label = "Quality Score",
                        data = weaponStats.Values.Select(w => w.QualityScore).ToArray(),
                        backgroundColor = "rgba(75, 192, 192, 0.6)",
                        borderColor = "rgba(75, 192, 192, 1)",
                        borderWidth = 1
                    },
                    new
                    {
                        label = "Recoil Intensity",
                        data = weaponStats.Values.Select(w => 
                            Math.Sqrt(Math.Pow(w.RecoilStats.PitchMaxMean, 2) + Math.Pow(w.RecoilStats.YawMaxMean, 2))
                        ).ToArray(),
                        backgroundColor = "rgba(255, 99, 132, 0.6)",
                        borderColor = "rgba(255, 99, 132, 1)",
                        borderWidth = 1
                    }
                }
            };

            var json = JsonConvert.SerializeObject(chartData, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json);
        }

        private static string SanitizeVariableName(string name)
        {
            // Convert weapon name to valid C++ variable name
            var sanitized = new StringBuilder();
            
            foreach (char c in name.ToLowerInvariant())
            {
                if (char.IsLetterOrDigit(c))
                {
                    sanitized.Append(c);
                }
                else if (c == ' ' || c == '-' || c == '.')
                {
                    sanitized.Append('_');
                }
            }

            var result = sanitized.ToString();
            
            // Ensure it starts with a letter
            if (result.Length > 0 && char.IsDigit(result[0]))
            {
                result = "weapon_" + result;
            }

            return result;
        }

        public async Task<string> CreateExportPackageAsync(
            List<OptimizedRecoilPattern> patterns, 
            Dictionary<string, WeaponStatistics> weaponStats, 
            string detailedReport,
            string outputDirectory)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var packageDir = Path.Combine(outputDirectory, $"recoil_analysis_{timestamp}");
            
            Directory.CreateDirectory(packageDir);

            // Export all formats
            await ExportOptimizedPatternsAsync(patterns, Path.Combine(packageDir, "optimized_patterns.json"));
            await ExportDetailedReportAsync(detailedReport, Path.Combine(packageDir, "analysis_report.txt"));
            await ExportCsvDataAsync(weaponStats, Path.Combine(packageDir, "weapon_statistics.csv"));
            await ExportRecoilPatternsForESP32Async(patterns, Path.Combine(packageDir, "recoil_patterns.h"));
            await ExportComparisonChartDataAsync(weaponStats, Path.Combine(packageDir, "chart_data.json"));

            // Create README
            var readme = new StringBuilder();
            readme.AppendLine("# Recoil Pattern Analysis Results");
            readme.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            readme.AppendLine();
            readme.AppendLine("## Files Included:");
            readme.AppendLine("- `optimized_patterns.json` - Optimized recoil patterns in JSON format");
            readme.AppendLine("- `analysis_report.txt` - Detailed statistical analysis report");
            readme.AppendLine("- `weapon_statistics.csv` - Weapon statistics in CSV format for spreadsheet analysis");
            readme.AppendLine("- `recoil_patterns.h` - C++ header file for ESP32 integration");
            readme.AppendLine("- `chart_data.json` - Data for creating comparison charts");
            readme.AppendLine();
            readme.AppendLine("## Top Weapons by Quality Score:");
            
            var topWeapons = weaponStats.OrderByDescending(w => w.Value.QualityScore).Take(5);
            foreach (var weapon in topWeapons)
            {
                readme.AppendLine($"- {weapon.Key}: {weapon.Value.QualityScore:F2}/100 (based on {weapon.Value.TotalRuns} runs)");
            }

            await File.WriteAllTextAsync(Path.Combine(packageDir, "README.md"), readme.ToString());

            return packageDir;
        }
    }
}
