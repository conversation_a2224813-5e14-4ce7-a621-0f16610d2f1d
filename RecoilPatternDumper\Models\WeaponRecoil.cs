// File: WeaponRecoil.cs
// Made by Lag

using System.Collections.Generic;

namespace RecoilPatternDumper.Models
{
    public class WeaponRecoil
    {
        public double YawMax { get; set; }
        public double YawMin { get; set; }
        public double PitchMax { get; set; }
        public double PitchMin { get; set; }
        public double TimeMin { get; set; }
        public double TimeMax { get; set; }
        public double AdsScale { get; set; }
        public double MaxRadius { get; set; }
        public List<double> PitchCurve { get; set; } = new();
        public List<double> YawCurve { get; set; } = new();
    }
}
