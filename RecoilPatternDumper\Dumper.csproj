<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- ✅ Custom Output Paths to avoid antivirus issues -->
    <OutputPath>$(SolutionDir)Build\</OutputPath>
    <IntermediateOutputPath>$(SolutionDir)Build\obj\</IntermediateOutputPath>

    <!-- ✅ Optional: Disable apphost.exe creation -->
    <UseAppHost>false</UseAppHost>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AssetsTools.NET" Version="3.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Diagnostics.Process" Version="4.3.0" />
    <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
  </ItemGroup>

</Project>
